/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-07 11:49:13
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-05-07 17:40:03
 * @FilePath: \wp-sitemgr\app\api\providers\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { getConnection } from '@/app/lib/db';

// Use the connection pool
const sql = getConnection();

export async function POST(request: Request) {
  try {
    const { name, type, accountId, region, apiKey, apiSecret, capabilities } = await request.json();
    
    // Insert the new provider into the database
    const result = await sql`
      INSERT INTO cloud_providers 
        (name, type, account_id, region, api_key, api_secret, capabilities, active)
      VALUES 
        (${name}, ${type}, ${accountId}, ${region}, ${apiKey}, ${apiSecret}, ${JSON.stringify(capabilities)}::jsonb, true)
      RETURNING id, name, type, account_id as "accountId", region, capabilities, active
    `;
    
    return NextResponse.json({ 
      success: true, 
      provider: result[0] 
    });
  } catch (error) {
    console.error('Error creating provider:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create provider' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const providers = await sql`
      SELECT 
        id, 
        name, 
        type, 
        account_id as "accountId", 
        region, 
        capabilities::json as capabilities, 
        active
      FROM cloud_providers
      ORDER BY name ASC
    `;
    
    // Ensure capabilities is properly parsed for each provider
    const parsedProviders = providers.map(provider => ({
      ...provider,
      capabilities: typeof provider.capabilities === 'string' 
        ? JSON.parse(provider.capabilities) 
        : (provider.capabilities || {})
    }));
    
    return NextResponse.json({ 
      success: true, 
      providers: parsedProviders 
    });
  } catch (error) {
    console.error('Error fetching providers:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch providers' },
      { status: 500 }
    );
  }
}




