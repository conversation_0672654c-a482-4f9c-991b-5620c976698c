import { NextResponse } from 'next/server';
import { getConnection } from '@/app/lib/db';
import type { NextApiRequest } from 'next';

interface RouteHandlerContext {
  params: Promise<{ id: string }>
}

// GET /api/sites/:id - Get a specific WordPress site

export async function GET(
  request: NextApiRequest & Request,
  context: RouteHandlerContext
) {
  const sql = getConnection();
  
  try {
    const { id } = await context.params;
    
    const result = await sql`
      SELECT 
        s.id, 
        s.name, 
        s.domain, 
        s.status,
        s.deployment_status as "deploymentStatus",
        s.deployment_log as "deploymentLog",
        s.admin_url as "adminUrl",
        s.admin_user as "adminUser",
        s.version,
        s.php_version as "phpVersion",
        s.cdn_enabled as "cdnEnabled",
        s.cdn_provider as "cdnProvider",
        s.created_at as "createdAt",
        s.updated_at as "updatedAt",
        dt.id as "templateId",
        dt.name as "templateName",
        dt.cloud_platform as "cloudPlatform",
        dt.deployment_type as "deploymentType",
        dt.environment_type as "environmentType"
      FROM wordpress_sites s
      LEFT JOIN deployment_templates dt ON s.template_id = dt.id
      WHERE s.id = ${id}
    `;
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: 'WordPress site not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(result[0]);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch WordPress site' },
      { status: 500 }
    );
  }
}

// PATCH /api/sites/:id - Update deployment status
export async function PATCH(
  request: NextApiRequest & Request,
  context: RouteHandlerContext
) {
  const sql = getConnection();
  
  try {
    const { id } = await context.params;
    const { deploymentStatus, deploymentLog } = await request.json();
    
    const result = await sql`
      UPDATE wordpress_sites 
      SET 
        deployment_status = ${deploymentStatus},
        deployment_log = CASE 
          WHEN ${deploymentLog} IS NOT NULL 
          THEN COALESCE(deployment_log, '') || ${deploymentLog}
          ELSE deployment_log
        END,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, deployment_status as "deploymentStatus"
    `;
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: 'WordPress site not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ 
      success: true, 
      id: result[0].id,
      deploymentStatus: result[0].deploymentStatus
    });
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to update deployment status' },
      { status: 500 }
    );
  }
}

// DELETE /api/sites/:id - Delete a WordPress site
export async function DELETE(
  request: NextApiRequest & Request,
  context: RouteHandlerContext
) {
  const sql = getConnection();
  
  try {
    const { id } = await context.params;
    
    const result = await sql`
      DELETE FROM wordpress_sites 
      WHERE id = ${id}
      RETURNING id
    `;
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: 'WordPress site not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to delete WordPress site' },
      { status: 500 }
    );
  }
}


