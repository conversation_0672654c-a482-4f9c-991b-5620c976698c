'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { wordpressDefaults } from '@/app/lib/config';

export default function CreateTemplatePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    cloudPlatform: 'aliyun',
    deploymentType: 'standard',
    environmentType: 'ecs',
    dnsProvider: 'aliyun_dns',
    cdnProvider: 'cloudflare',
    wpVersion: wordpressDefaults.version,
    phpVersion: wordpressDefaults.version,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Redirect back to templates page on success
        router.push('/dashboard/templates');
      } else {
        // Handle error
        console.error('Failed to create template:', data.error);
        // You could set an error state here to display to the user
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      // You could set an error state here to display to the user
    }
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">创建部署模板</h1>
        <p className="text-gray-600">为WordPress站点部署定义一个新模板</p>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">模板名称</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="cloudPlatform" className="block text-sm font-medium text-gray-700">云平台</label>
            <select
              id="cloudPlatform"
              name="cloudPlatform"
              value={formData.cloudPlatform}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="aliyun">Aliyun</option>
              <option value="aws">AWS</option>
              <option value="azure">Azure</option>
              <option value="gcp">Google Cloud</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="deploymentType" className="block text-sm font-medium text-gray-700">部署类型</label>
            <select
              id="deploymentType"
              name="deploymentType"
              value={formData.deploymentType}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="standard">Standard</option>
              <option value="kubernetes">Kubernetes</option>
              <option value="serverless">Serverless</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="environmentType" className="block text-sm font-medium text-gray-700">环境类型</label>
            <select
              id="environmentType"
              name="environmentType"
              value={formData.environmentType}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              {formData.cloudPlatform === 'aliyun' && (
                <>
                  <option value="ecs">ECS Instance</option>
                  <option value="ack">Container Service (ACK)</option>
                  <option value="fc">Function Compute</option>
                </>
              )}
              {formData.cloudPlatform === 'aws' && (
                <>
                  <option value="ec2">EC2 Instance</option>
                  <option value="eks">Elastic Kubernetes Service (EKS)</option>
                  <option value="lambda">Lambda</option>
                </>
              )}
              {formData.cloudPlatform === 'azure' && (
                <>
                  <option value="vm">Virtual Machine</option>
                  <option value="aks">Azure Kubernetes Service</option>
                  <option value="functions">Azure Functions</option>
                </>
              )}
              {formData.cloudPlatform === 'gcp' && (
                <>
                  <option value="compute">Compute Engine</option>
                  <option value="gke">Google Kubernetes Engine</option>
                  <option value="functions">Cloud Functions</option>
                </>
              )}
            </select>
          </div>
          
          <div>
            <label htmlFor="dnsProvider" className="block text-sm font-medium text-gray-700">DNS提供商</label>
            <select
              id="dnsProvider"
              name="dnsProvider"
              value={formData.dnsProvider}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="aliyun_dns">Aliyun DNS</option>
              <option value="route53">AWS Route 53</option>
              <option value="cloudflare">Cloudflare</option>
              <option value="azure_dns">Azure DNS</option>
              <option value="cloud_dns">Google Cloud DNS</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="cdnProvider" className="block text-sm font-medium text-gray-700">CDN提供商</label>
            <select
              id="cdnProvider"
              name="cdnProvider"
              value={formData.cdnProvider}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="cloudflare">Cloudflare</option>
              <option value="aliyun_cdn">Aliyun CDN</option>
              <option value="cloudfront">AWS CloudFront</option>
              <option value="azure_cdn">Azure CDN</option>
              <option value="cloud_cdn">Google Cloud CDN</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="wpVersion" className="block text-sm font-medium text-gray-700">WordPress版本</label>
            <select
              id="wpVersion"
              name="wpVersion"
              value={formData.wpVersion}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="6.8.2">6.8.2 (default)</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="phpVersion" className="block text-sm font-medium text-gray-700">PHP版本</label>
            <select
              id="phpVersion"
              name="phpVersion"
              value={formData.phpVersion}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="8.2">8.2</option>
              <option value="8.1">8.1</option>
              <option value="8.0">8.0</option>
              <option value="7.4">7.4</option>
            </select>
          </div>
        </div>
        
        <div className="flex justify-end space-x-3">
          <Link
            href="/dashboard/templates"
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            取消
          </Link>
          <button
            type="submit"
            className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            创建模板
          </button>
        </div>
      </form>
    </div>
  );
}
