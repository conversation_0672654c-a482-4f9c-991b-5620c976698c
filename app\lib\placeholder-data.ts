// Placeholder data for seeding the database
import { randomUUID } from 'crypto';

export const users = [
  {
    id: '410544b2-**************-fec4b6a6442a',
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'password123',
  },
  {
    id: '410544b2-**************-fec4b6a6442b',
    name: 'Developer User',
    email: '<EMAIL>',
    password: 'password123',
  },
];

export const customers = [
  {
    id: '3958dc9e-712f-4377-85e9-fec4b6a6442a',
    name: 'Acme Corp',
    email: '<EMAIL>',
    image_url: '/customers/acme.png',
  },
  {
    id: '3958dc9e-712f-4377-85e9-fec4b6a6442b',
    name: 'Globex Inc',
    email: '<EMAIL>',
    image_url: '/customers/globex.png',
  },
  {
    id: '3958dc9e-712f-4377-85e9-fec4b6a6442c',
    name: 'Stark Industries',
    email: '<EMAIL>',
    image_url: '/customers/stark.png',
  },
];

export const invoices = [
  {
    customer_id: '3958dc9e-712f-4377-85e9-fec4b6a6442a',
    amount: 15795,
    status: 'pending',
    date: '2023-12-06',
  },
  {
    customer_id: '3958dc9e-712f-4377-85e9-fec4b6a6442b',
    amount: 20348,
    status: 'pending',
    date: '2023-11-14',
  },
  {
    customer_id: '3958dc9e-712f-4377-85e9-fec4b6a6442c',
    amount: 3040,
    status: 'paid',
    date: '2023-10-29',
  },
  {
    customer_id: '3958dc9e-712f-4377-85e9-fec4b6a6442a',
    amount: 44800,
    status: 'paid',
    date: '2023-09-10',
  },
];

export const revenue = [
  { month: 'Jan', revenue: 2000 },
  { month: 'Feb', revenue: 1800 },
  { month: 'Mar', revenue: 2200 },
  { month: 'Apr', revenue: 2500 },
  { month: 'May', revenue: 2300 },
  { month: 'Jun', revenue: 3200 },
  { month: 'Jul', revenue: 3500 },
  { month: 'Aug', revenue: 3700 },
  { month: 'Sep', revenue: 2500 },
  { month: 'Oct', revenue: 2800 },
  { month: 'Nov', revenue: 3000 },
  { month: 'Dec', revenue: 4800 },
];

