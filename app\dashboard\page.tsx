/*
 * @Author: zhou<PERSON> <EMAIL>
 * @Date: 2025-05-07 09:57:08
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-07-29 15:44:02
 * @FilePath: \wp-sitemgr\app\dashboard\page.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { Suspense } from 'react';
import { Card } from '@/app/ui/dashboard/cards';

export default function Page() {
  return (
    <main>
      <h1 className="mb-4 text-xl md:text-2xl">
        WordPress站点管理
      </h1>
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <Card
          title="总的WordPress站点"
          value="0"
          type="wordpress"
        />
        <Card
          title="活动服务器"
          value="0"
          type="server"
        />
        <Card
          title="云平台"
          value="0"
          type="provider"
        />
        <Card
          title="启用CDN的站点"
          value="0"
          type="cdn"
        />
      </div>
      <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Suspense fallback={<div>加载中...</div>}>
          <div className="rounded-xl bg-gray-50 p-4">
            <h2 className="mb-4 text-xl font-medium">最近WordPress站点</h2>
            <p className="text-gray-500">还没有添加站点</p>
          </div>
        </Suspense>
        <Suspense fallback={<div>加载中...</div>}>
          <div className="rounded-xl bg-gray-50 p-4">
            <h2 className="mb-4 text-xl font-medium">服务器状态</h2>
            <p className="text-gray-500">还没有添加服务器</p>
          </div>
        </Suspense>
        <Suspense fallback={<div>加载中...</div>}>
          <div className="rounded-xl bg-gray-50 p-4">
            <h2 className="mb-4 text-xl font-medium">CDN状态</h2>
            <p className="text-gray-500">还没有配置CDN</p>
          </div>
        </Suspense>
      </div>
    </main>
  );
}