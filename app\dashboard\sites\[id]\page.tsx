'use client';

import { useState, useEffect,use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeftIcon, ArrowPathIcon, GlobeAltIcon, ServerIcon, CloudIcon, ShieldCheckIcon, TrashIcon } from '@heroicons/react/24/outline';

interface WordPressSite {
  id: string;
  name: string;
  domain: string;
  status: string;
  deploymentStatus: string;
  deploymentLog: string;
  adminUrl: string;
  adminUser: string;
  version: string;
  phpVersion: string;
  cdnEnabled: boolean;
  cdnProvider: string;
  createdAt: string;
  updatedAt: string;
  templateId: string;
  templateName: string;
  cloudPlatform: string;
  deploymentType: string;
  environmentType: string;
}

export default function SiteDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const router = useRouter();
  const [site, setSite] = useState<WordPressSite | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeploying, setIsDeploying] = useState(false);

  useEffect(() => {
    async function fetchSite() {
      try {
        const response = await fetch(`/api/sites/${id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch site details');
        }
        const data = await response.json();
        setSite(data);
      } catch (err) {
        setError('Error loading site details');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchSite();
  }, [id]);

  // Function to handle deployment
  const handleDeploy = async () => {
    if (!site) return;
    
    try {
      setIsDeploying(true);
      
      // Update deployment status to 'deploying'
      const updateResponse = await fetch(`/api/sites/${site.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deploymentStatus: 'deploying',
          deploymentLog: `[${new Date().toISOString()}] Starting deployment process...\n`
        }),
      });
      
      if (!updateResponse.ok) {
        throw new Error('Failed to update deployment status');
      }
      
      // Refresh site data
      const refreshResponse = await fetch(`/api/sites/${site.id}`);
      if (!refreshResponse.ok) {
        throw new Error('Failed to refresh site data');
      }
      
      const updatedSite = await refreshResponse.json();
      setSite(updatedSite);
      
      // Simulate deployment steps (in a real app, this would be handled by a backend service)
      await simulateDeployment(site.id);
      
      // Final refresh after deployment
      const finalResponse = await fetch(`/api/sites/${site.id}`);
      if (!finalResponse.ok) {
        throw new Error('Failed to get final site data');
      }
      
      const finalSite = await finalResponse.json();
      setSite(finalSite);
      
    } catch (err) {
      console.error('Deployment error:', err);
      setError('Deployment failed. Please try again.');
      
      // Update status to failed
      try {
        await fetch(`/api/sites/${site.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            deploymentStatus: 'failed',
            deploymentLog: `[${new Date().toISOString()}] Deployment failed: ${err}\n`
          }),
        });
      } catch (updateErr) {
        console.error('Failed to update status to failed:', updateErr);
      }
    } finally {
      setIsDeploying(false);
    }
  };
  
  // Function to simulate deployment steps
  const simulateDeployment = async (siteId: string) => {
    const steps = [
      { message: 'Provisioning server resources...', delay: 2000 },
      { message: 'Setting up database...', delay: 3000 },
      { message: 'Installing WordPress...', delay: 3000 },
      { message: 'Configuring web server...', delay: 2000 },
      { message: 'Setting up SSL certificate...', delay: 2000 },
      { message: 'Configuring DNS...', delay: 1500 },
      { message: 'Finalizing deployment...', delay: 1500 }
    ];
    
    for (const step of steps) {
      // Update deployment log with current step
      await fetch(`/api/sites/${siteId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deploymentLog: `[${new Date().toISOString()}] ${step.message}\n`
        }),
      });
      
      // Refresh site data to show updated log
      const response = await fetch(`/api/sites/${siteId}`);
      const updatedSite = await response.json();
      setSite(updatedSite);
      
      // Wait for the specified delay
      await new Promise(resolve => setTimeout(resolve, step.delay));
    }
    
    // Mark deployment as completed
    await fetch(`/api/sites/${siteId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        deploymentStatus: 'completed',
        deploymentLog: `[${new Date().toISOString()}] Deployment completed successfully!\n`
      }),
    });
  };
  
  // Function to refresh site data
  const refreshSite = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/sites/${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to refresh site data');
      }
      const data = await response.json();
      setSite(data);
      setError(null);
    } catch (err) {
      setError('Error refreshing site data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  // Function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };
  
  // Function to get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'deploying':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <ArrowPathIcon className="h-8 w-8 text-gray-400 animate-spin" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
        <button 
          onClick={refreshSite}
          className="mt-2 text-sm underline"
        >
          重试
        </button>
      </div>
    );
  }
  
  if (!site) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
        <p>站点不存在或已被删除</p>
        <Link
          href="/dashboard/sites"
          className="mt-2 text-sm underline"
        >
          返回站点列表
        </Link>
      </div>
    );
  }
  
  return (
    <div className="w-full">
      <div className="flex items-center mb-6">
        <Link
          href="/dashboard/sites"
          className="mr-4 text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold">{site.name}</h1>
          <p className="text-gray-600">{site.domain}</p>
        </div>
        <div className="ml-auto flex space-x-2">
          <button
            onClick={refreshSite}
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50"
            disabled={loading}
          >
            <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <Link
            href={`/dashboard/sites/${site.id}/edit`}
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            编辑
          </Link>
          <button
            onClick={handleDeploy}
            disabled={isDeploying || site.deploymentStatus === 'deploying'}
            className={`flex items-center gap-1 rounded-md px-3 py-1.5 text-sm font-medium ${
              isDeploying || site.deploymentStatus === 'deploying'
                ? 'bg-blue-300 text-white cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isDeploying || site.deploymentStatus === 'deploying' ? (
              <>
                <ArrowPathIcon className="h-4 w-4 animate-spin" />
                部署中...
              </>
            ) : (
              <>
                <ServerIcon className="h-4 w-4" />
                部署
              </>
            )}
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium">站点信息</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">基本信息</h3>
                  <div className="mt-2 space-y-3">
                    <div>
                      <div className="text-xs text-gray-500">站点名称</div>
                      <div className="text-sm font-medium">{site.name}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">域名</div>
                      <div className="text-sm font-medium">{site.domain}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">状态</div>
                      <div className="text-sm">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(site.status)}`}>
                          {site.status === 'active' ? '活跃' : site.status}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">创建时间</div>
                      <div className="text-sm">{formatDate(site.createdAt)}</div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">技术信息</h3>
                  <div className="mt-2 space-y-3">
                    <div>
                      <div className="text-xs text-gray-500">WordPress版本</div>
                      <div className="text-sm">{site.version}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">PHP版本</div>
                      <div className="text-sm">{site.phpVersion}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">部署模板</div>
                      <div className="text-sm">{site.templateName}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">云平台</div>
                      <div className="text-sm">{site.cloudPlatform}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">环境类型</div>
                      <div className="text-sm">{site.environmentType}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {site.adminUrl && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-500">管理信息</h3>
                  <div className="mt-2 space-y-3">
                    <div>
                      <div className="text-xs text-gray-500">管理后台</div>
                      <div className="text-sm">
                        <a href={site.adminUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                          {site.adminUrl}
                        </a>
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">管理员用户名</div>
                      <div className="text-sm">{site.adminUser}</div>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-500">CDN配置</h3>
                <div className="mt-2">
                  <div className="flex items-center">
                    <div className="text-sm">
                      {site.cdnEnabled ? (
                        <span className="text-green-600 flex items-center">
                          <ShieldCheckIcon className="h-4 w-4 mr-1" />
                          已启用 ({site.cdnProvider})
                        </span>
                      ) : (
                        <span className="text-gray-500">未启用</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6 bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium">部署状态</h2>
              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(site.deploymentStatus)}`}>
                {site.deploymentStatus === 'completed' && '已完成'}
                {site.deploymentStatus === 'pending' && '等待中'}
                {site.deploymentStatus === 'deploying' && '部署中'}
                {site.deploymentStatus === 'failed' && '失败'}
              </span>
            </div>
            <div className="p-6">
              <div className="bg-gray-50 rounded border border-gray-200 p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">部署日志</h3>
                <pre className="text-xs text-gray-600 whitespace-pre-wrap h-64 overflow-y-auto">
                  {site.deploymentLog || '无部署日志'}
                </pre>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium">快速操作</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {site.adminUrl && (
                  <a
                    href={site.adminUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-blue-500 hover:text-blue-700"
                  >
                    <GlobeAltIcon className="h-5 w-5" />
                    <span>访问WordPress管理后台</span>
                  </a>
                )}
                <a
                  href={`https://${site.domain}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-blue-500 hover:text-blue-700"
                >
                  <GlobeAltIcon className="h-5 w-5" />
                  <span>访问站点前台</span>
                </a>
                <button
                  onClick={handleDeploy}
                  disabled={isDeploying || site.deploymentStatus === 'deploying'}
                  className="flex items-center gap-2 text-blue-500 hover:text-blue-700 disabled:text-gray-400 disabled:hover:text-gray-400"
                >
                  <ServerIcon className="h-5 w-5" />
                  <span>重新部署站点</span>
                </button>
                {site.cdnEnabled ? (
                  <button className="flex items-center gap-2 text-blue-500 hover:text-blue-700">
                    <CloudIcon className="h-5 w-5" />
                    <span>刷新CDN缓存</span>
                  </button>
                ) : (
                  <button className="flex items-center gap-2 text-blue-500 hover:text-blue-700">
                    <CloudIcon className="h-5 w-5" />
                    <span>启用CDN</span>
                  </button>
                )}
              </div>
            </div>
          </div>
          
          <div className="mt-6 bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium">危险操作</h2>
            </div>
            <div className="p-6">
              <button
                onClick={() => {
                  if (confirm('确定要删除此WordPress站点吗？此操作不可撤销。')) {
                    fetch(`/api/sites/${site.id}`, { method: 'DELETE' })
                      .then(response => {
                        if (!response.ok)
                          throw new Error('Failed to delete site');
                        router.push('/dashboard/sites');
                      })
                      .catch(error => {
                        console.error('Error deleting site:', error);
                        setError('Error deleting site. Please try again.');
                      });
                  }
                }}
                className="flex items-center gap-2 text-red-500 hover:text-red-700"
              >
                <TrashIcon className="h-5 w-5" />
                <span>删除站点</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


