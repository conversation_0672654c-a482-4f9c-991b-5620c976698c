'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusIcon, GlobeAltIcon, ServerIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface WordPressSite {
  id: string;
  name: string;
  domain: string;
  status: string;
  deploymentStatus: string;
  adminUrl: string;
  version: string;
  phpVersion: string;
  cdnEnabled: boolean;
  cdnProvider: string;
  createdAt: string;
  updatedAt: string;
  templateName: string;
  cloudPlatform: string;
  deploymentType: string;
  environmentType: string;
}



export default function WordPressSitesPage() {
  const [sites, setSites] = useState<WordPressSite[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    async function fetchSites() {
      try {
        const response = await fetch('/api/sites');
        if (!response.ok) {
          throw new Error('Failed to fetch sites');
        }
        const data = await response.json();
        setSites(data);
      } catch (err) {
        setError('Error loading WordPress sites');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchSites();
  }, []);

  // Function to handle deployment status refresh
  const refreshDeploymentStatus = async (siteId: string) => {
    try {
      const response = await fetch(`/api/sites/${siteId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch site status');
      }
      const updatedSite = await response.json();
      
      // Update the site in the sites array
      setSites(prevSites => 
        prevSites.map(site => 
          site.id === siteId ? { ...site, deploymentStatus: updatedSite.deploymentStatus } : site
        )
      );
    } catch (err) {
      console.error('Error refreshing deployment status:', err);
    }
  };

  // Function to get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'deploying':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">WordPress站点</h1>
          <p className="text-gray-600">管理您的WordPress站点</p>
        </div>
        <Link
          href="/dashboard/sites/new"
          className="flex items-center gap-1 rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
        >
          <PlusIcon className="h-5 w-5" />
          新站点
        </Link>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <ArrowPathIcon className="h-8 w-8 text-gray-400 animate-spin" />
        </div>
      ) : sites.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
          <GlobeAltIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900">没有WordPress站点</h3>
          <p className="mt-2 text-gray-500">创建您的第一个WordPress站点。</p>
          <Link
            href="/dashboard/sites/new"
            className="mt-4 inline-flex items-center rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            创建站点
          </Link>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  站点名称
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  域名
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  部署模板
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  部署状态
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  WordPress版本
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sites.map((site) => (
                <tr key={site.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{site.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{site.domain}</div>
                    {site.adminUrl && (
                      <a href={site.adminUrl} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-500 hover:underline">
                        管理后台
                      </a>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{site.templateName}</div>
                    <div className="text-xs text-gray-500">
                      {site.cloudPlatform} / {site.deploymentType}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(site.deploymentStatus)}`}>
                        {site.deploymentStatus === 'completed' && '已完成'}
                        {site.deploymentStatus === 'pending' && '等待中'}
                        {site.deploymentStatus === 'deploying' && '部署中'}
                        {site.deploymentStatus === 'failed' && '失败'}
                      </span>
                      <button 
                        onClick={() => refreshDeploymentStatus(site.id)}
                        className="ml-2 text-gray-400 hover:text-gray-500"
                        title="刷新状态"
                      >
                        <ArrowPathIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">WordPress {site.version}</div>
                    <div className="text-xs text-gray-500">PHP {site.phpVersion}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link href={`/dashboard/sites/${site.id}`} className="text-blue-600 hover:text-blue-900 mr-4">
                      详情
                    </Link>
                    <Link href={`/dashboard/sites/${site.id}/edit`} className="text-indigo-600 hover:text-indigo-900">
                      编辑
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}