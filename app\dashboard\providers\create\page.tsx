'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function CreateProviderPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    type: 'aliyun',
    accountId: '',
    region: '',
    apiKey: '',
    apiSecret: '',
    capabilities: {
      rds: true,
      loadbalancer: true,
      k8s: false
    }
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCapabilityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      capabilities: {
        ...prev.capabilities,
        [name]: checked
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/providers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Redirect back to providers page on success
        router.push('/dashboard/providers');
      } else {
        // Handle error
        console.error('Failed to create provider:', data.error);
        // You could set an error state here to display to the user
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      // You could set an error state here to display to the user
    }
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Add Cloud Provider</h1>
        <p className="text-gray-600">Connect a new cloud provider account</p>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">Provider Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
              placeholder="e.g., Aliyun Production"
            />
          </div>
          
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700">Provider Type</label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="aliyun">Aliyun</option>
              <option value="aws">AWS</option>
              <option value="azure">Azure</option>
              <option value="gcp">Google Cloud</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="accountId" className="block text-sm font-medium text-gray-700">Account ID</label>
            <input
              type="text"
              id="accountId"
              name="accountId"
              value={formData.accountId}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="region" className="block text-sm font-medium text-gray-700">Region</label>
            <select
              id="region"
              name="region"
              value={formData.region}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              {formData.type === 'aliyun' && (
                <>
                  <option value="ap-southeast-1">Singapore (ap-southeast-1)</option>
                  <option value="ap-northeast-1">Tokyo (ap-northeast-1)</option>
                  <option value="cn-hangzhou">Hangzhou (cn-hangzhou)</option>
                  <option value="cn-beijing">Beijing (cn-beijing)</option>
                  <option value="cn-shanghai">Shanghai (cn-shanghai)</option>
                </>
              )}
              {formData.type === 'aws' && (
                <>
                  <option value="us-east-1">US East (N. Virginia)</option>
                  <option value="us-west-2">US West (Oregon)</option>
                  <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                  <option value="eu-west-1">EU (Ireland)</option>
                </>
              )}
            </select>
          </div>
          
          <div>
            <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700">API Key</label>
            <input
              type="text"
              id="apiKey"
              name="apiKey"
              value={formData.apiKey}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
            />
          </div>
          
          <div>
            <label htmlFor="apiSecret" className="block text-sm font-medium text-gray-700">API Secret</label>
            <input
              type="password"
              id="apiSecret"
              name="apiSecret"
              value={formData.apiSecret}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
            />
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Capabilities</label>
          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="rds"
                name="rds"
                checked={formData.capabilities.rds}
                onChange={handleCapabilityChange}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="rds" className="ml-2 block text-sm text-gray-700">
                RDS Database Management
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="loadbalancer"
                name="loadbalancer"
                checked={formData.capabilities.loadbalancer}
                onChange={handleCapabilityChange}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="loadbalancer" className="ml-2 block text-sm text-gray-700">
                Load Balancer Management
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="k8s"
                name="k8s"
                checked={formData.capabilities.k8s}
                onChange={handleCapabilityChange}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="k8s" className="ml-2 block text-sm text-gray-700">
                Kubernetes Cluster Management
              </label>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => router.push('/dashboard/providers')}
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Add Provider
          </button>
        </div>
      </form>
    </div>
  );
}


