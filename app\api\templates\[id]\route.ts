import { NextResponse } from 'next/server';
import { prisma } from '@/app/lib/prisma';

// GET /api/templates/:id - Get a specific template
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const template = await prisma.deploymentTemplate.findUnique({
      where: { id },
      include: {
        businessGroup: true
      }
    });
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // Transform to match expected format
    const result = {
      id: template.id,
      name: template.name,
      description: template.description,
      cloudPlatform: template.businessGroup.cloudPlatform,
      deploymentType: template.businessGroup.deploymentType,
      environmentType: template.businessGroup.environmentType,
      dnsProvider: template.businessGroup.dnsProvider,
      cdnProvider: null, // Not in schema
      wpVersion: template.wpVersion,
      phpVersion: null, // Not in schema
      businessGroupId: template.businessGroupId,
      settings: template.settings
    };
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch template' },
      { status: 500 }
    );
  }
}

// PUT /api/templates/:id - Update a template
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { 
      name, 
      description, 
      wpVersion,
      settings,
      businessGroupId
    } = await request.json();
    
    // Validate required fields
    if (!name || !wpVersion) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    const template = await prisma.deploymentTemplate.update({
      where: { id },
      data: {
        name,
        description,
        wpVersion,
        settings,
        businessGroupId
      }
    });
    
    return NextResponse.json({ success: true, id: template.id });
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to update template' },
      { status: 500 }
    );
  }
}

// DELETE /api/templates/:id - Delete a template
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    await prisma.deploymentTemplate.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 }
    );
  }
}







