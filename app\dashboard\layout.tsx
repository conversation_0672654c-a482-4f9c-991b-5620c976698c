/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-07 09:57:03
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-26 10:22:35
 * @FilePath: \wp-sitemgr\app\dashboard\layout.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import SideNav from '@/app/ui/dashboard/sidenav';
import Header from '@/app/ui/dashboard/header';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex h-screen flex-col">
      <Header />
      <div className="flex flex-1 md:overflow-hidden">
        <div className="w-full flex-none md:w-64">
          <SideNav />
        </div>
        <div className="flex-1 p-6 md:overflow-y-auto md:p-12">{children}</div>
      </div>
    </div>
  );
}



