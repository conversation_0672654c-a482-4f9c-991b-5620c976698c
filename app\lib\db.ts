/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-08 11:14:24
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-07-30 17:28:33
 * @FilePath: \wp-sitemgr\app\lib\db.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import postgres from 'postgres';
import { dbConfig } from './config';

// Create a singleton connection pool that can be reused across requests
let pg: ReturnType<typeof postgres> | null = null;

export function getConnection() {
  if (!pg) {
    // Initialize the connection pool only once
    pg = postgres(dbConfig.url, {
      ssl: dbConfig.sslRequired ? 'require' : undefined,
      max: dbConfig.poolSize || 10, // Connection pool size
      idle_timeout: 20, // Seconds a connection can be idle before being removed
      connect_timeout: dbConfig.connectionTimeout || 30000, // Connection timeout in ms
    });
  }
  
  return pg;
}