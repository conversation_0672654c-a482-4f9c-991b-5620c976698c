/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-08-26 11:25:09
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-09-29 17:26:30
 * @FilePath: \wp-sitemgr\app\api\dns\check\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { dnsConfig } from '@/app/lib/config';

import { prisma } from '@/app/lib/prisma';

// POST /api/dns/check - Check DNS resolution
export async function POST(request: Request) {
  const setRecords = async (records: Object) => {
    try {
      const record = await prisma.domainRecord.create({
        data: {
          domain: nameDomain,
          type: records['type'],
          value: records['content'],
          ttl: 300,
          proxy: records['proxied'] == '是' ? true : false,
          comment: '',
          provider: 'cloudflare'
        }
      });
      console.log('Record created:', record);
    } catch (error) {
      console.error('Error creating record:', error);
    }
  };
  let nameDomain: string = '';
  try {
    let resolved = false;
    let records: Object = {};
    let err = '';
    const requestData = await request.json();
    nameDomain = requestData.domain;

    if (!nameDomain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      );
    }
    try {
      const result = await fetch(`${dnsConfig.dnsBaseServerApi}/api/cfcheck`, {
        method: 'POST',
        headers: {
          'auth-header': `${process.env.DNS_MANAGER_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ nameDomain: nameDomain })
      });
      if (result.status != 200) {
        throw new Error(`请求解析接口失败 ${result.status}`);
      }

      resolved = true;
      const res = await result.json();
      console.log(res);

      if (res.error ) {
        throw new Error(res.error);
      }
      records = Object.assign({}, { name: res.record.name, type: res.record.type, content: res.record.content, proxied: res.record.proxied == true? '是' : '否' });
      //新增解析记录
      setRecords(records);
      
    } catch (error) {
      resolved = false;
      records = {};
      err = error instanceof Error ? error.message : 'Unknown error occurred';
    }
    return NextResponse.json({
      domain: nameDomain,
      resolved,
      records,
      error: err,
      message: resolved ? 'Domain is resolved' : 'Domain is not resolved'
    });
} catch (error) {
  console.error('DNS check error:', error);
  if (error instanceof Error && 'code' in error && error.code === 'ENOTFOUND') {
    return NextResponse.json({
      domain: nameDomain,
      resolved: false,
      records: {},
      message: 'Domain is not resolved'
    });
  }
  return NextResponse.json(
    { error: 'Failed to check DNS resolution' },
    { status: 500 }
  );
}
}
