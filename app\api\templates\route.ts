/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-08 10:58:49
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-30 11:46:45
 * @FilePath: \wp-sitemgr\app\api\templates\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';

// GET /api/templates - Get all templates
export async function GET() {
  try {
    const templates = await prisma.deploymentTemplate.findMany({
      include: {
        businessGroup: {
          select: {
            cloudPlatform: true,
            deploymentType: true,
            environmentType: true,
            dnsProvider: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });
    
    // Transform to include business group data
    const transformedTemplates = templates.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      cloudPlatform: template.businessGroup.cloudPlatform,
      deploymentType: template.businessGroup.deploymentType,
      environmentType: template.businessGroup.environmentType,
      dnsProvider: template.businessGroup.dnsProvider,
      wpVersion: template.wpVersion,
      settings: template.settings,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt
    }));
    
    return NextResponse.json(transformedTemplates);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}

// POST /api/templates - Create a new template
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    const template = await prisma.deploymentTemplate.create({
      data: {
        name: data.name,
        businessGroupId: data.businessGroup,
        description: data.description,
        wpVersion: data.wpVersion,
        settings: data.settings || {},
      }
    });
    
    return NextResponse.json({ success: true, template });
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
}






