/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-08 13:53:02
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-07-31 11:41:26
 * @FilePath: \wp-sitemgr\app\api\sites\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { prisma } from '@/app/lib/prisma';

// GET /api/sites - Get all WordPress sites
export async function GET() {
  try {
    const sites = await prisma.wordpressSite.findMany({
      include: {
        template: true,
        server: true,
        loadBalancer: true,
      },
      orderBy: { createdAt: 'desc' }
    });
    
    return NextResponse.json(sites);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch WordPress sites' },
      { status: 500 }
    );
  }
}

// POST /api/sites - Create a new WordPress site
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    const site = await prisma.wordpressSite.create({
      data: {
        name: data.name,
        subName: data.subName,
        templateId: data.templateId,
        adminUser: data.adminUser,
        adminPassword: data.adminPassword,
        description: data.description,
        version: data.version,
        phpVersion: data.phpVersion,
        deploymentStatus: 'pending',
      }
    });
    
    return NextResponse.json(site);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to create WordPress site' },
      { status: 500 }
    );
  }
}


