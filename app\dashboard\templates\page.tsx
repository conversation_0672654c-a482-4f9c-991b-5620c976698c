/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-07 11:20:32
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-30 11:24:33
 * @FilePath: \wp-sitemgr\app\dashboard\templates\page.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

interface Template {
  id: string;
  name: string;
  description: string;
  cloudPlatform: string;
  deploymentType: string;
  environmentType: string;
  dnsProvider: string;
  cdnProvider: string;
  wpVersion: string;
  phpVersion: string;
  settings?: {
    plugins?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/templates');
        
        if (!response.ok) {
          throw new Error('加载部署模板失败');
        }
        
        const data = await response.json();
        setTemplates(data);
        setError(null);
      } catch (err) {
        console.error('加载部署模板失败:', err);
        setError('加载部署模板失败，请稍后再试。');
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除此部署模板吗？')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/templates/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('删除部署模板失败');
      }
      
      setTemplates(templates.filter(template => template.id !== id));
    } catch (err) {
      console.error('删除部署模板失败:', err);
      setError('删除部署模板失败，请稍后再试。');
    }
  };

  if (loading) {
    return (
      <div className="w-full flex justify-center items-center py-10">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
          <p className="mt-2">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full text-center py-10">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="mt-2 text-sm underline"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">部署模板</h1>
          <p className="text-gray-600">管理WordPress部署模板</p>
        </div>
        <Link
          href="/dashboard/templates/new"
          className="flex items-center gap-1 rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
        >
          <PlusIcon className="h-5 w-5" />
          创建新部署模板
        </Link>
      </div>
      
      {templates.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <p className="text-gray-500 mb-4">没有找到部署模板</p>
          <Link
            href="/dashboard/templates/new"
            className="inline-flex items-center gap-1 text-blue-500 hover:text-blue-700"
          >
            <PlusIcon className="h-5 w-5" />
            创建第一个部署模板
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  名称
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  云平台
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  部署类型
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  WordPress版本
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  预装插件
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">操作</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {templates.map((template) => (
                <tr key={template.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{template.name}</div>
                    {template.description && (
                      <div className="text-sm text-gray-500 truncate max-w-xs">{template.description}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 capitalize">{template.cloudPlatform || 'N/A'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 capitalize">{template.deploymentType || 'N/A'}</div>
                    <div className="text-sm text-gray-500 capitalize">{template.environmentType}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">WP {template.wpVersion}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {template.settings?.plugins?.length ? (
                        <div className="flex flex-wrap gap-1">
                          {template.settings.plugins.slice(0, 3).map((plugin) => (
                            <span key={plugin} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {plugin}
                            </span>
                          ))}
                          {template.settings.plugins.length > 3 && (
                            <span className="text-xs text-gray-500">+{template.settings.plugins.length - 3} 更多</span>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">无</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Link
                        href={`/dashboard/templates/edit/${template.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <PencilIcon className="h-5 w-5" />
                        <span className="sr-only">编辑</span>
                      </Link>
                      <button
                        onClick={() => handleDelete(template.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-5 w-5" />
                        <span className="sr-only">删除</span>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
