'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function NewBusinessGroupPage() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    cloudPlatform: 'aliyun',
    deploymentType: 'standard',
    environmentType: 'ecs',
    dnsProvider: 'cloudflare',
    apiEndpoint: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/business-groups', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create business group');
      }

      router.push('/dashboard/business-groups');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create business group');
    }
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">创建业务组</h1>
        <p className="text-gray-600">配置新的业务组和部署设置</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">名称</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="code" className="block text-sm font-medium text-gray-700">代码</label>
            <input
              type="text"
              id="code"
              name="code"
              value={formData.code}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
              placeholder="例如: edm, shop, marketing"
            />
          </div>

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            />
          </div>

          <div>
            <label htmlFor="cloudPlatform" className="block text-sm font-medium text-gray-700">云平台</label>
            <select
              id="cloudPlatform"
              name="cloudPlatform"
              value={formData.cloudPlatform}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="aliyun">阿里云</option>
              <option value="aws">AWS</option>
              <option value="azure">Azure</option>
              <option value="gcp">Google Cloud</option>
            </select>
          </div>

          <div>
            <label htmlFor="deploymentType" className="block text-sm font-medium text-gray-700">部署类型</label>
            <select
              id="deploymentType"
              name="deploymentType"
              value={formData.deploymentType}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="standard">宿主机部署</option>
              <option value="kubernetes">K8S</option>
            </select>
          </div>

          <div>
            <label htmlFor="environmentType" className="block text-sm font-medium text-gray-700">环境类型</label>
            <select
              id="environmentType"
              name="environmentType"
              value={formData.environmentType}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              {formData.cloudPlatform === 'aliyun' && (
                <>
                  <option value="ecs">ECS实例</option>
                  <option value="ack">容器服务ACK</option>
                </>
              )}
              {formData.cloudPlatform === 'aws' && (
                <>
                  <option value="ec2">EC2实例</option>
                  <option value="eks">EKS</option>
                </>
              )}
            </select>
          </div>

          <div>
            <label htmlFor="dnsProvider" className="block text-sm font-medium text-gray-700">DNS提供商</label>
            <select
              id="dnsProvider"
              name="dnsProvider"
              value={formData.dnsProvider}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="cloudflare">Cloudflare</option>
              <option value="aliyun_dns">阿里云DNS</option>
              <option value="route53">AWS Route 53</option>
            </select>
          </div>

          <div className="md:col-span-2">
            <label htmlFor="apiEndpoint" className="block text-sm font-medium text-gray-700">API地址</label>
            <input
              type="url"
              id="apiEndpoint"
              name="apiEndpoint"
              value={formData.apiEndpoint}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
              placeholder="https://api.example.com/deploy"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <Link
            href="/dashboard/business-groups"
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            取消
          </Link>
          <button
            type="submit"
            className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
          >
            创建业务组
          </button>
        </div>
      </form>
    </div>
  );
}