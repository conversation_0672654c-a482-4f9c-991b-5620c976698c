-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cloud_providers" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "account_id" TEXT,
    "api_key" TEXT,
    "api_secret" TEXT,
    "region" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "capabilities" JSONB NOT NULL DEFAULT '{}',
    "settings" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "cloud_providers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "servers" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "provider_id" TEXT,
    "instance_id" TEXT,
    "ip_address" TEXT,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "specs" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "servers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "business_groups" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "cloud_platform" TEXT NOT NULL,
    "deployment_type" TEXT NOT NULL,
    "environment_type" TEXT NOT NULL,
    "dns_provider" TEXT NOT NULL,
    "api_endpoint" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "business_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "deployment_templates" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "business_group_id" TEXT NOT NULL,
    "description" TEXT,
    "wp_version" TEXT NOT NULL,
    "settings" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "deployment_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "wordpress_sites" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "sub_name" TEXT,
    "domain" TEXT,
    "server_id" TEXT,
    "rds_account_id" TEXT,
    "load_balancer_id" TEXT,
    "template_id" TEXT,
    "admin_url" TEXT,
    "admin_user" TEXT,
    "admin_password" TEXT,
    "description" TEXT,
    "version" TEXT,
    "php_version" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "cdn_enabled" BOOLEAN NOT NULL DEFAULT false,
    "cdn_provider" TEXT,
    "cdn_config" JSONB,
    "deployment_status" TEXT NOT NULL DEFAULT 'pending',
    "deployment_log" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "wordpress_sites_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rds_instances" (
    "id" TEXT NOT NULL,
    "provider_id" TEXT,
    "instance_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "endpoint" TEXT,
    "port" INTEGER NOT NULL DEFAULT 3306,
    "engine" TEXT NOT NULL DEFAULT 'MySQL',
    "engine_version" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "rds_instances_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rds_accounts" (
    "id" TEXT NOT NULL,
    "rds_instance_id" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "password" TEXT,
    "privileges" TEXT NOT NULL DEFAULT 'ReadWrite',
    "status" TEXT NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "rds_accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "load_balancers" (
    "id" TEXT NOT NULL,
    "provider_id" TEXT,
    "lb_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'Application',
    "ip_address" TEXT,
    "dns_name" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "settings" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "load_balancers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lb_listeners" (
    "id" TEXT NOT NULL,
    "load_balancer_id" TEXT NOT NULL,
    "protocol" TEXT NOT NULL,
    "port" INTEGER NOT NULL,
    "target_group" TEXT,
    "certificate_arn" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lb_listeners_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cloudfronts" (
    "id" TEXT NOT NULL,
    "provider_id" TEXT,
    "cloudfront_id" TEXT NOT NULL,
    "origin_domain" TEXT,
    "distribution_status" TEXT NOT NULL,
    "settings" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "cloudfronts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "domain_records" (
    "id" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "provider" TEXT NOT NULL DEFAULT 'cloudflare',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "domain_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "deployment_steps" (
    "id" TEXT NOT NULL,
    "template_id" TEXT NOT NULL,
    "step_order" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "parameters" JSONB,

    CONSTRAINT "deployment_steps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_BusinessGroupToWordpressSite" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "business_groups_name_key" ON "business_groups"("name");

-- CreateIndex
CREATE UNIQUE INDEX "business_groups_code_key" ON "business_groups"("code");

-- CreateIndex
CREATE UNIQUE INDEX "_BusinessGroupToWordpressSite_AB_unique" ON "_BusinessGroupToWordpressSite"("A", "B");

-- CreateIndex
CREATE INDEX "_BusinessGroupToWordpressSite_B_index" ON "_BusinessGroupToWordpressSite"("B");

-- AddForeignKey
ALTER TABLE "servers" ADD CONSTRAINT "servers_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "cloud_providers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "deployment_templates" ADD CONSTRAINT "deployment_templates_business_group_id_fkey" FOREIGN KEY ("business_group_id") REFERENCES "business_groups"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "wordpress_sites" ADD CONSTRAINT "wordpress_sites_server_id_fkey" FOREIGN KEY ("server_id") REFERENCES "servers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "wordpress_sites" ADD CONSTRAINT "wordpress_sites_rds_account_id_fkey" FOREIGN KEY ("rds_account_id") REFERENCES "rds_accounts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "wordpress_sites" ADD CONSTRAINT "wordpress_sites_load_balancer_id_fkey" FOREIGN KEY ("load_balancer_id") REFERENCES "load_balancers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "wordpress_sites" ADD CONSTRAINT "wordpress_sites_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "deployment_templates"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rds_instances" ADD CONSTRAINT "rds_instances_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "cloud_providers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rds_accounts" ADD CONSTRAINT "rds_accounts_rds_instance_id_fkey" FOREIGN KEY ("rds_instance_id") REFERENCES "rds_instances"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "load_balancers" ADD CONSTRAINT "load_balancers_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "cloud_providers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lb_listeners" ADD CONSTRAINT "lb_listeners_load_balancer_id_fkey" FOREIGN KEY ("load_balancer_id") REFERENCES "load_balancers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cloudfronts" ADD CONSTRAINT "cloudfronts_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "cloud_providers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "deployment_steps" ADD CONSTRAINT "deployment_steps_template_id_fkey" FOREIGN KEY ("template_id") REFERENCES "deployment_templates"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_BusinessGroupToWordpressSite" ADD CONSTRAINT "_BusinessGroupToWordpressSite_A_fkey" FOREIGN KEY ("A") REFERENCES "business_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_BusinessGroupToWordpressSite" ADD CONSTRAINT "_BusinessGroupToWordpressSite_B_fkey" FOREIGN KEY ("B") REFERENCES "wordpress_sites"("id") ON DELETE CASCADE ON UPDATE CASCADE;
