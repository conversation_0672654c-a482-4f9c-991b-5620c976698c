{"private": true, "scripts": {"build": "next build", "dev": "next dev --turbopack", "start": "next start", "postinstall": "prisma generate", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@next/react-refresh-utils": "^15.3.2", "@prisma/client": "^5.0.0", "@tailwindcss/forms": "^0.5.10", "@types/bcryptjs": "^3.0.0", "autoprefixer": "10.4.20", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "next": "latest", "next-auth": "5.0.0-beta.25", "postcss": "8.5.1", "postgres": "^3.4.5", "prisma": "^5.0.0", "react": "latest", "react-dom": "latest", "react-refresh": "^0.17.0", "react-select": "^5.10.2", "tailwindcss": "3.4.17", "typescript": "5.7.3", "use-debounce": "^10.0.4", "zod": "^3.24.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "22.10.7", "@types/react": "19.0.7", "@types/react-dom": "19.0.3", "prisma": "^5.0.0"}}