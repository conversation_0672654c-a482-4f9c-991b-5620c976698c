import {
  GlobeAltIcon,
  ServerIcon,
  CloudIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { lusitana } from '@/app/ui/fonts';

const iconMap = {
  wordpress: GlobeAltIcon,
  server: ServerIcon,
  provider: CloudIcon,
  cdn: ShieldCheckIcon,
};

export function Card({
  title,
  value,
  type,
}: {
  title: string;
  value: string;
  type: 'wordpress' | 'server' | 'provider' | 'cdn';
}) {
  const Icon = iconMap[type];

  return (
    <div className="rounded-xl bg-white p-2 shadow-sm">
      <div className="flex p-4">
        {Icon ? <Icon className="h-5 w-5 text-gray-700" /> : null}
        <h3 className="ml-2 text-sm font-medium">{title}</h3>
      </div>
      <p className={`${lusitana.className} truncate rounded-xl bg-gray-50 px-4 py-8 text-center text-2xl`}>
        {value}
      </p>
    </div>
  );
}


