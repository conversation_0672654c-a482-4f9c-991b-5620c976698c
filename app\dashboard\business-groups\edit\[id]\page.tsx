'use client';

import { useState, useEffect } from 'react';
import { use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function EditBusinessGroupPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    cloudPlatform: 'aliyun',
    deploymentType: 'standard',
    environmentType: 'ecs',
    dnsProvider: 'cloudflare',
    apiEndpoint: '',
    active: true,
  });

  useEffect(() => {
    fetchBusinessGroup();
  }, []);

  const fetchBusinessGroup = async () => {
    try {
      const response = await fetch(`/api/business-groups/${id}`);
      if (!response.ok) throw new Error('Failed to fetch business group');
      const data = await response.json();           
      
      // Ensure all values are strings, not null
      setFormData({
        name: data.name || '',
        code: data.code || '',
        description: data.description || '',
        cloudPlatform: data.cloudPlatform || 'aliyun',
        deploymentType: data.deploymentType || 'standard',
        environmentType: data.environmentType || 'ecs',
        dnsProvider: data.dnsProvider || 'cloudflare',
        apiEndpoint: data.apiEndpoint || '',
        active: data.active ?? true,
      });
    } catch (err) {
      setError('Failed to load business group');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    setFormData((prev) => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch(`/api/business-groups/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update business group');
      }

      router.push('/dashboard/business-groups');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update business group');
    }
  };

  if (loading) return <div className="text-center py-10">加载中...</div>;

  return (
    <div className="w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">编辑业务组</h1>
        <p className="text-gray-600">修改业务组配置和部署设置</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
        {/* Same form fields as create page */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="active" className="flex items-center">
              <input
                type="checkbox"
                id="active"
                name="active"
                checked={formData.active}
                onChange={handleChange}
                className="mr-2"
              />
              <span className="text-sm font-medium text-gray-700">启用业务组</span>
            </label>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <Link
            href="/dashboard/business-groups"
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            取消
          </Link>
          <button
            type="submit"
            className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
          >
            更新业务组
          </button>
        </div>
      </form>
    </div>
  );
}

