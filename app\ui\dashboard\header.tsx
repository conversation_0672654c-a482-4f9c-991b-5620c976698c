import { lusitana } from '@/app/ui/fonts';
import { UserCircleIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

export default function Header() {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <h1 className={`${lusitana.className} text-2xl font-bold text-gray-900`}>
            WP站点管理
          </h1>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-gray-700">
            <UserCircleIcon className="h-8 w-8 text-gray-400" />
            <div className="flex flex-col">
              <span className="font-medium">管理员</span>
              <span className="text-xs text-gray-500"><EMAIL></span>
            </div>
            <ChevronDownIcon className="h-4 w-4 text-gray-400" />
          </div>
        </div>
      </div>
    </header>
  );
}