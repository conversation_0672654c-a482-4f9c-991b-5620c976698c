/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-08-30 13:34:02
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-30 16:34:37
 * @FilePath: \wp-sitemgr\app\api\dns\records\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { prisma } from '@/app/lib/prisma';

// GET /api/dns/records - Get all DNS records
export async function GET() {
  try {
    const records = await prisma.domainRecord.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10
    });
    
    return NextResponse.json( records );
  } catch (error) {
    console.error('Error fetching DNS records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch DNS records' },
      { status: 500 }
    );
  }
}

// POST /api/dns/records - Add new DNS record
export async function POST(request: Request) {
  try {
    const { domain, type, value, ttl, provider } = await request.json();
    
    if (!domain || !type || !value) {
      return NextResponse.json(
        { error: 'Domain, type, and value are required' },
        { status: 400 }
      );
    }
    
    // Call external DNS provider API to add record
    const result = await addDnsRecordToProvider({
      domain,
      type,
      value,
      // ttl: ttl || 300,
      provider
    });
    
    if (!result.success) {
      return NextResponse.json(
        { error: (result as any).error || 'Failed to add DNS record' },
        { status: 400 }
      );
    }
    
    // In production, save to database
    // Use a more predictable ID generation for consistency
    const recordId = `${domain}-${type}-${value}`.replace(/[^a-zA-Z0-9-]/g, '-').toLowerCase();
    const timestamp = new Date().toISOString();

    const newRecord = {
      id: recordId,
      domain,
      type,
      value,
      // ttl: ttl || 300,
      status: 'pending',
      provider,
      createdAt: timestamp
    };
    
    return NextResponse.json({ success: true, record: newRecord });
  } catch (error) {
    console.error('Error adding DNS record:', error);
    return NextResponse.json(
      { error: 'Failed to add DNS record' },
      { status: 500 }
    );
  }
}

async function addDnsRecordToProvider(record: any) {
  const { domain, type, value, ttl, provider } = record;
  
  try {
    if (provider === 'cloudflare') {
      return await addCloudflareRecord({ domain, type, value, ttl });
    } else if (provider === 'aliyun_dns') {
      return await addAliyunRecord({ domain, type, value, ttl });
    } else if (provider === 'route53') {
      return await addRoute53Record({ domain, type, value, ttl });
    }
    
    return { success: false, error: 'Unsupported DNS provider' };
  } catch (error) {
    console.error('DNS provider API error:', error);
    return { success: false, error: 'DNS provider API error' };
  }
}

async function addCloudflareRecord({ domain, type, value, ttl }: any) {
  const cfApiToken = process.env.CLOUDFLARE_API_TOKEN;
  const cfZoneId = process.env.CLOUDFLARE_ZONE_ID;
  
  if (!cfApiToken || !cfZoneId) {
    // Simulate success for demo
    return { success: true };
  }
  
  try {
    const response = await fetch(`https://api.cloudflare.com/client/v4/zones/${cfZoneId}/dns_records`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${cfApiToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type,
        name: domain,
        content: value,
        ttl
      })
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      return { success: false, error: data.errors?.[0]?.message || 'Cloudflare API error' };
    }
    
    return { success: true, recordId: data.result?.id };
  } catch (error) {
    return { success: false, error: 'Failed to connect to Cloudflare API' };
  }
}

async function addAliyunRecord({ domain, type, value, ttl }: any) {
  // Implement Aliyun DNS API call
  // For demo, simulate success
  return { success: true };
}

async function addRoute53Record({ domain, type, value, ttl }: any) {
  // Implement AWS Route 53 API call
  // For demo, simulate success
  return { success: true };
}