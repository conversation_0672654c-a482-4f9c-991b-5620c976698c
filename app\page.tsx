/*
 * @Author: zhou<PERSON> <EMAIL>
 * @Date: 2025-05-06 17:43:10
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-05-08 13:40:10
 * @FilePath: \wp-sitemgr\app\page.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import AcmeLogo from '@/app/ui/acme-logo';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

export default function Page() {
  return (
    <main className="flex min-h-screen flex-col p-6">
      <div className="flex h-20 shrink-0 items-end rounded-lg bg-blue-500 p-4 md:h-52">
        <AcmeLogo />
      </div>
      <div className="mt-4 flex grow flex-col gap-4 md:flex-row">
        <div className="flex flex-col justify-center gap-6 rounded-lg bg-gray-50 px-6 py-10 md:w-2/5 md:px-20">
          <p className={`text-xl text-gray-800 md:text-3xl md:leading-normal`}>
            <strong>WordPress站点管理</strong>多云部署和CDN集成
          </p>
          <div className="flex flex-col gap-3 text-sm md:text-base">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-blue-500"></span>
              <span>在阿里云和AWS上管理WordPress站点</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-blue-500"></span>
              <span>支持服务器实例和Kubernetes</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-blue-500"></span>
              <span>Cloudflare和CloudFront CDN集成</span>
            </div>
          </div>
          <Link
            href="/dashboard"
            className="flex items-center gap-5 self-start rounded-lg bg-blue-500 px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-blue-400 md:text-base"
          >
            <span>仪表板</span> <ArrowRightIcon className="w-5 md:w-6" />
          </Link>
        </div>
        <div className="flex items-center justify-center p-6 md:w-3/5 md:px-28 md:py-12">
          <div className="relative h-full w-full">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 opacity-20 rounded-lg"></div>
            <div className="relative p-6 flex flex-col gap-4">
              <h3 className="text-xl font-semibold">多云WordPress管理</h3>
              <p>跨不同云平台的WordPress站点集中仪表板</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}


