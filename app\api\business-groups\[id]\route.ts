import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// GET /api/business-groups/[id] - Get single business group
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const businessGroup = await prisma.businessGroup.findUnique({
      where: { id },
      include: {
        templates: true,
        _count: {
          select: { wordpressSites: true }
        }
      }
    });
    
    if (!businessGroup) {
      return NextResponse.json(
        { error: 'Business group not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(businessGroup);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch business group' },
      { status: 500 }
    );
  }
}

// PUT /api/business-groups/[id] - Update business group
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const data = await request.json();
    
    const businessGroup = await prisma.businessGroup.update({
      where: { id },
      data: {
        name: data.name,
        code: data.code,
        description: data.description,
        cloudPlatform: data.cloudPlatform,
        deploymentType: data.deploymentType,
        environmentType: data.environmentType,
        dnsProvider: data.dnsProvider,
        apiEndpoint: data.apiEndpoint,
        active: data.active,
      }
    });
    
    return NextResponse.json(businessGroup);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to update business group' },
      { status: 500 }
    );
  }
}

// DELETE /api/business-groups/[id] - Delete business group
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    await prisma.businessGroup.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to delete business group' },
      { status: 500 }
    );
  }
}
