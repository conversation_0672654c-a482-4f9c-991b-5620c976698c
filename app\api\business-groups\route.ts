/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-08-26 11:24:49
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-26 11:26:54
 * @FilePath: \wp-sitemgr\app\api\business-groups\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';

// GET /api/business-groups - Get all business groups
export async function GET() {
  try {
    const businessGroups = await prisma.businessGroup.findMany({
      include: {
        templates: true,
        _count: {
          select: { wordpressSites: true }
        }
      },
      orderBy: { name: 'asc' }
    });
    
    return NextResponse.json(businessGroups);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch business groups' },
      { status: 500 }
    );
  }
}

// POST /api/business-groups - Create a new business group
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    const businessGroup = await prisma.businessGroup.create({
      data: {
        name: data.name,
        code: data.code,
        description: data.description,
        cloudPlatform: data.cloudPlatform,
        deploymentType: data.deploymentType,
        environmentType: data.environmentType,
        dnsProvider: data.dnsProvider,
        apiEndpoint: data.apiEndpoint,
      }
    });
    
    return NextResponse.json(businessGroup);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to create business group' },
      { status: 500 }
    );
  }
}