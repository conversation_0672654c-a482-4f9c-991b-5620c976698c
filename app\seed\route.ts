import { NextResponse } from 'next/server';
import bcrypt from 'bcrypt';
import postgres from 'postgres';
import { dbConfig } from '../lib/config';
import { invoices, customers, revenue, users } from '../lib/placeholder-data';

// Initialize SQL with proper error handling
const sql = postgres(dbConfig.url, { 
  ssl: dbConfig.sslRequired ? 'require' : undefined,
  onnotice: () => {}, // Suppress notice messages
  debug: false, // Disable debug logging
  max: 1, // Limit pool size for seeding
  idle_timeout: 20 // Shorter timeout for seeding operations
});

async function seedUsers() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL
      );
    `;

    // Check if users already exist to avoid duplicate seeding
    const existingUsers = await sql`SELECT COUNT(*) FROM users`;
    if (parseInt(existingUsers[0].count) > 0) {
      console.log('Users already seeded, skipping...');
      return { success: true, message: 'Users already exist' };
    }

    const insertedUsers = await Promise.all(
      users.map(async (user) => {
        const hashedPassword = await bcrypt.hash(user.password, 10);
        return sql`
          INSERT INTO users (id, name, email, password)
          VALUES (${user.id}, ${user.name}, ${user.email}, ${hashedPassword})
          ON CONFLICT (id) DO NOTHING;
        `;
      }),
    );

    return { success: true, count: insertedUsers.length };
  } catch (error) {
    console.error('Error seeding users:', error);
    return { success: false, error };
  }
}

async function seedCloudProviders() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    await sql`
      CREATE TABLE IF NOT EXISTS cloud_providers (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL,
        account_id VARCHAR(100),
        api_key TEXT,
        api_secret TEXT,
        region VARCHAR(100),
        active BOOLEAN DEFAULT true,
        capabilities JSONB DEFAULT '{"rds": false, "loadbalancer": false, "k8s": false}'::jsonb,
        settings JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Check if cloud providers already exist
    const existingProviders = await sql`SELECT COUNT(*) FROM cloud_providers`;
    if (parseInt(existingProviders[0].count) > 0) {
      console.log('Cloud providers already seeded, skipping...');
      return { success: true, message: 'Cloud providers already exist' };
    }
    
    // Insert some sample cloud providers
    await sql`
      INSERT INTO cloud_providers 
        (name, type, account_id, region, active, capabilities)
      VALUES 
        ('Aliyun Production', 'aliyun', '************', 'ap-southeast-1', true, '{"rds": true, "loadbalancer": true, "k8s": true}'::jsonb),
        ('Aliyun Development', 'aliyun', '************', 'ap-southeast-1', true, '{"rds": true, "loadbalancer": true, "k8s": false}'::jsonb),
        ('Aliyun Testing', 'aliyun', '************', 'ap-northeast-1', true, '{"rds": true, "loadbalancer": false, "k8s": false}'::jsonb),
        ('AWS Production', 'aws', '************', 'us-west-2', true, '{"rds": true, "loadbalancer": true, "k8s": true}'::jsonb)
      ON CONFLICT DO NOTHING;
    `;
    
    return { success: true };
  } catch (error) {
    console.error('Error seeding cloud providers:', error);
    return { success: false, error };
  }
}

async function seedServers() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    await sql`
      CREATE TABLE IF NOT EXISTS servers (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        provider_id UUID,
        instance_id VARCHAR(255),
        ip_address VARCHAR(45),
        type VARCHAR(50) NOT NULL,
        status VARCHAR(50) DEFAULT 'active',
        specs JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Add foreign key constraint if cloud_providers table exists
    try {
      await sql`
        ALTER TABLE servers 
        ADD CONSTRAINT fk_servers_provider 
        FOREIGN KEY (provider_id) 
        REFERENCES cloud_providers(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      // Constraint might already exist, which is fine
      console.log('Note: Foreign key constraint may already exist for servers table');
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error seeding servers:', error);
    return { success: false, error };
  }
}

async function seedRdsInstances() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    await sql`
      CREATE TABLE IF NOT EXISTS rds_instances (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        provider_id UUID,
        instance_id VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        endpoint VARCHAR(255),
        port INT DEFAULT 3306,
        engine VARCHAR(50) DEFAULT 'MySQL',
        engine_version VARCHAR(20),
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Add foreign key constraint if cloud_providers table exists
    try {
      await sql`
        ALTER TABLE rds_instances 
        ADD CONSTRAINT fk_rds_provider 
        FOREIGN KEY (provider_id) 
        REFERENCES cloud_providers(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      // Constraint might already exist, which is fine
      console.log('Note: Foreign key constraint may already exist for rds_instances table');
    }
    
    await sql`
      CREATE TABLE IF NOT EXISTS rds_accounts (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        rds_instance_id UUID NOT NULL,
        username VARCHAR(255) NOT NULL,
        password TEXT,
        privileges VARCHAR(50) DEFAULT 'ReadWrite',
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Add foreign key constraint if rds_instances table exists
    try {
      await sql`
        ALTER TABLE rds_accounts 
        ADD CONSTRAINT fk_rds_accounts_instance 
        FOREIGN KEY (rds_instance_id) 
        REFERENCES rds_instances(id) 
        ON DELETE CASCADE;
      `;
    } catch (error) {
      // Constraint might already exist, which is fine
      console.log('Note: Foreign key constraint may already exist for rds_accounts table');
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error seeding RDS instances:', error);
    return { success: false, error };
  }
}

async function seedLoadBalancers() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    await sql`
      CREATE TABLE IF NOT EXISTS load_balancers (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        provider_id UUID,
        lb_id VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) DEFAULT 'Application',
        ip_address VARCHAR(45),
        dns_name VARCHAR(255),
        status VARCHAR(50) DEFAULT 'active',
        settings JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Add foreign key constraint if cloud_providers table exists
    try {
      await sql`
        ALTER TABLE load_balancers 
        ADD CONSTRAINT fk_lb_provider 
        FOREIGN KEY (provider_id) 
        REFERENCES cloud_providers(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      // Constraint might already exist, which is fine
      console.log('Note: Foreign key constraint may already exist for load_balancers table');
    }
    
    await sql`
      CREATE TABLE IF NOT EXISTS lb_listeners (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        load_balancer_id UUID NOT NULL,
        protocol VARCHAR(10) NOT NULL,
        port INT NOT NULL,
        target_group VARCHAR(255),
        certificate_arn VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Add foreign key constraint if load_balancers table exists
    try {
      await sql`
        ALTER TABLE lb_listeners 
        ADD CONSTRAINT fk_listeners_lb 
        FOREIGN KEY (load_balancer_id) 
        REFERENCES load_balancers(id) 
        ON DELETE CASCADE;
      `;
    } catch (error) {
      // Constraint might already exist, which is fine
      console.log('Note: Foreign key constraint may already exist for lb_listeners table');
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error seeding load balancers:', error);
    return { success: false, error };
  }
}


async function seedCloudfronts() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    await sql`
      CREATE TABLE IF NOT EXISTS cloudfronts (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        provider_id UUID,
        cloudfront_id VARCHAR(255) NOT NULL,
        origin_domain VARCHAR(255),
        distribution_status VARCHAR(50) NOT NULL,
        settings JSONB NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Add foreign key constraint if cloud_providers table exists
    try {
      await sql`
        ALTER TABLE cloudfronts 
        ADD CONSTRAINT fk_cf_provider 
        FOREIGN KEY (provider_id) 
        REFERENCES cloud_providers(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      // Constraint might already exist, which is fine
      console.log('Note: Foreign key constraint may already exist for cloudfronts table');
    }
    return { success: true };
  } catch (error) {
    console.error('Error seeding cloudfronts:', error);
    return { success: false, error };
  }
}

async function seedDeploymentTemplates() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    await sql`
      CREATE TABLE IF NOT EXISTS deployment_templates (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        cloud_platform VARCHAR(50) NOT NULL,
        deployment_type VARCHAR(50) NOT NULL,
        environment_type VARCHAR(50) NOT NULL,
        dns_provider VARCHAR(50),
        cdn_provider VARCHAR(50),
        wp_version VARCHAR(20),
        php_version VARCHAR(10),
        settings JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Check if templates already exist
    const existingTemplates = await sql`SELECT COUNT(*) FROM deployment_templates`;
    if (parseInt(existingTemplates[0].count) > 0) {
      console.log('Deployment templates already seeded, skipping...');
      return { success: true, message: '部署模板已经存在' };
    }
    
    // Add some default templates
    await sql`
      INSERT INTO deployment_templates 
        (name, description, cloud_platform, deployment_type, environment_type, dns_provider, cdn_provider, wp_version, php_version, settings)
      VALUES 
        ('Aliyun ECS Standard', 'Standard WordPress deployment on Aliyun ECS with RDS', 'aliyun', 'standard', 'ecs', 'aliyun_dns', 'cloudflare', '6.4', '8.1', '{"instance_type": "ecs.g6.large", "disk_size": 40}'::jsonb),
        ('Aliyun ACK Kubernetes', 'WordPress deployment on Aliyun Container Service for Kubernetes', 'aliyun', 'kubernetes', 'ack', 'aliyun_dns', 'cloudflare', '6.4', '8.1', '{"cluster_type": "managed", "node_count": 2}'::jsonb),
        ('Aliyun Serverless', 'Serverless WordPress deployment on Aliyun FC', 'aliyun', 'serverless', 'fc', 'aliyun_dns', 'cloudflare', '6.4', '8.1', '{"memory": 1024, "timeout": 60}'::jsonb),
        ('AWS EC2 Standard', 'Standard WordPress deployment on AWS EC2 with RDS', 'aws', 'standard', 'ec2', 'route53', 'cloudfront', '6.4', '8.1', '{"instance_type": "t3.medium", "disk_size": 40}'::jsonb),
        ('AWS EKS Kubernetes', 'WordPress deployment on AWS Elastic Kubernetes Service', 'aws', 'kubernetes', 'eks', 'route53', 'cloudfront', '6.4', '8.1', '{"cluster_type": "managed", "node_count": 2}'::jsonb)
      ON CONFLICT DO NOTHING;
    `;
    
    await sql`
      CREATE TABLE IF NOT EXISTS deployment_steps (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        template_id UUID NOT NULL,
        step_order INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        action VARCHAR(100) NOT NULL,
        parameters JSONB
      );
    `;
    
    // Add foreign key constraint if deployment_templates table exists
    try {
      await sql`
        ALTER TABLE deployment_steps 
        ADD CONSTRAINT fk_steps_template 
        FOREIGN KEY (template_id) 
        REFERENCES deployment_templates(id) 
        ON DELETE CASCADE;
      `;
    } catch (error) {
      // Constraint might already exist, which is fine
      console.log('Note: Foreign key constraint may already exist for deployment_steps table');
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error seeding deployment templates:', error);
    return { success: false, error };
  }
}

async function seedWordpressSites() {
  try {
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    
    await sql`
      CREATE TABLE IF NOT EXISTS wordpress_sites (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        domain VARCHAR(255) NOT NULL,
        server_id UUID,
        rds_account_id UUID,
        load_balancer_id UUID,
        template_id UUID,
        admin_url VARCHAR(255),
        admin_user VARCHAR(100),
        admin_password TEXT,
        version VARCHAR(20),
        php_version VARCHAR(10),
        status VARCHAR(50) DEFAULT 'active',
        cdn_enabled BOOLEAN DEFAULT false,
        cdn_provider VARCHAR(50),
        cdn_config JSONB,
        deployment_status VARCHAR(50) DEFAULT 'completed',
        deployment_log TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Add foreign key constraints if related tables exist
    try {
      await sql`
        ALTER TABLE wordpress_sites 
        ADD CONSTRAINT fk_wp_server 
        FOREIGN KEY (server_id) 
        REFERENCES servers(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      console.log('Note: Foreign key constraint may already exist for wordpress_sites.server_id');
    }
    
    try {
      await sql`
        ALTER TABLE wordpress_sites 
        ADD CONSTRAINT fk_wp_rds 
        FOREIGN KEY (rds_account_id) 
        REFERENCES rds_accounts(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      console.log('Note: Foreign key constraint may already exist for wordpress_sites.rds_account_id');
    }
    
    try {
      await sql`
        ALTER TABLE wordpress_sites 
        ADD CONSTRAINT fk_wp_lb 
        FOREIGN KEY (load_balancer_id) 
        REFERENCES load_balancers(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      console.log('Note: Foreign key constraint may already exist for wordpress_sites.load_balancer_id');
    }
    
    try {
      await sql`
        ALTER TABLE wordpress_sites 
        ADD CONSTRAINT fk_wp_template 
        FOREIGN KEY (template_id) 
        REFERENCES deployment_templates(id) 
        ON DELETE SET NULL;
      `;
    } catch (error) {
      console.log('Note: Foreign key constraint may already exist for wordpress_sites.template_id');
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error seeding WordPress sites:', error);
    return { success: false, error };
  }
}

export async function GET() {
  try {
    // Wrap each seeding operation in a try/catch to prevent one failure from stopping the entire process
    const results = [];
    
    try {
      results.push({ users: await seedUsers() });
    } catch (error) {
      console.error('Error in seedUsers:', error);
      results.push({ users: { success: false, error: (error as Error).message || String(error) } });
    }
    
    try {
      results.push({ cloudProviders: await seedCloudProviders() });
    } catch (error) {
      console.error('Error in seedCloudProviders:', error);
      results.push({ cloudProviders: { success: false, error: (error as Error).message || String(error) } });
    }
    
    try {
      results.push({ servers: await seedServers() });
    } catch (error) {
      console.error('Error in seedServers:', error);
      results.push({ servers: { success: false, error: (error as Error).message || String(error) } });
    }
    
    try {
      results.push({ rdsInstances: await seedRdsInstances() });
    } catch (error) {
      console.error('Error in seedRdsInstances:', error);
      results.push({ rdsInstances: { success: false, error: (error as Error).message || String(error) } });
    }
    
    try {
      results.push({ loadBalancers: await seedLoadBalancers() });
    } catch (error) {
      console.error('Error in seedLoadBalancers:', error);
      results.push({ loadBalancers: { success: false, error: (error as Error).message || String(error) } });
    }
    
    try {
      results.push({ cloudfronts: await seedCloudfronts() });
    } catch (error) {
      console.error('Error in seedCloudfronts:', error);
      results.push({ cloudfronts: { success: false, error: (error as Error).message || String(error) } });
    }
    
    try {
      results.push({ deploymentTemplates: await seedDeploymentTemplates() });
    } catch (error) {
      console.error('Error in seedDeploymentTemplates:', error);
      results.push({ deploymentTemplates: { success: false, error: (error as Error).message || String(error) } });
    }
    
    try {
      results.push({ wordpressSites: await seedWordpressSites() });
    } catch (error) {
      console.error('Error in seedWordpressSites:', error);
      results.push({ wordpressSites: { success: false, error: (error as Error).message || String(error) } });
    }
    
    // Check if any operations failed
    const anyFailures = results.some(result => {
      const key = Object.keys(result)[0];
      return result[key as keyof typeof result] && !result[key as keyof typeof result]?.success;
    });
    
    if (anyFailures) {
      return NextResponse.json({ 
        message: 'Database seeding completed with some errors', 
        results 
      }, { status: 207 }); // 207 Multi-Status
    } else {
      return NextResponse.json({ 
        message: 'Database seeded successfully', 
        results 
      });
    }
  } catch (error) {
    console.error('Seed error:', error);
    return NextResponse.json({ 
      error: 'Database seeding failed', 
      details: (error as Error).message || String(error) 
    }, { status: 500 });
  } finally {
    // Close the connection pool to prevent hanging
    try {
      await sql.end();
    } catch (endError) {
      console.error('Error closing SQL connection:', endError);
    }
  }
}





