'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { wordpressDefaults } from '@/app/lib/config';

interface BusinessGroup {
  [x: string]: any;
  id: string;
  name: string;
  code: string;
  cloudPlatform: string;
  deploymentType: string;
  environmentType: string;
  dnsProvider: string;
}

export default function NewTemplatePage() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [businessGroups, setBusinessGroups] = useState<BusinessGroup[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    businessGroupId: '',
    description: '',
    wpVersion: wordpressDefaults.version,
    plugins: [] as string[],
  });

  const availablePlugins = [
    'woocommerce',
    'yoast-seo',
    'elementor',
    'contact-form-7',
    'wp-super-cache',
    'akismet',
    'jetpack',
    'wordfence',
    'wp-optimize',
    'updraftplus'
  ];

  useEffect(() => {
    fetchBusinessGroups();
  }, []);

  const fetchBusinessGroups = async () => {
    try {
      const response = await fetch('/api/business-groups');
      if (!response.ok) throw new Error('Failed to fetch business groups');
      const data = await response.json();
      setBusinessGroups(data.filter((bg: BusinessGroup) => bg.active));
    } catch (err) {
      setError('Failed to load business groups');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handlePluginChange = (plugin: string) => {
    setFormData((prev) => ({
      ...prev,
      plugins: prev.plugins.includes(plugin)
        ? prev.plugins.filter(p => p !== plugin)
        : [...prev.plugins, plugin]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          businessGroup: formData.businessGroupId,
          settings: { plugins: formData.plugins }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create template');
      }

      router.push('/dashboard/templates');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create template');
    }
  };

  const selectedBusinessGroup = businessGroups.find(bg => bg.id === formData.businessGroupId);

  return (
    <div className="w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">创建新部署模板</h1>
        <p className="text-gray-600">配置一个新的WordPress部署模板</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">模板名称</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="businessGroupId" className="block text-sm font-medium text-gray-700">业务组</label>
            <select
              id="businessGroupId"
              name="businessGroupId"
              value={formData.businessGroupId}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              required
            >
              <option value="">选择业务组</option>
              {businessGroups.map((bg) => (
                <option key={bg.id} value={bg.id}>{bg.name} ({bg.code})</option>
              ))}
            </select>
          </div>

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">描述</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            />
          </div>

          {selectedBusinessGroup && (
            <div className="md:col-span-2 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-2">业务组配置</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>云平台: {selectedBusinessGroup.cloudPlatform}</div>
                <div>部署类型: {selectedBusinessGroup.deploymentType}</div>
                <div>环境类型: {selectedBusinessGroup.environmentType}</div>
                <div>DNS提供商: {selectedBusinessGroup.dnsProvider}</div>
              </div>
            </div>
          )}

          <div>
            <label htmlFor="wpVersion" className="block text-sm font-medium text-gray-700">WordPress版本</label>
            <select
              id="wpVersion"
              name="wpVersion"
              value={formData.wpVersion}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
            >
              <option value="6.8.2">6.8.2 (默认)</option>
              <option value="6.7.1">6.7.1</option>
              <option value="6.6.2">6.6.2</option>
            </select>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-3">预装插件</label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availablePlugins.map((plugin) => (
                <label key={plugin} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.plugins.includes(plugin)}
                    onChange={() => handlePluginChange(plugin)}
                    className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{plugin}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <Link
            href="/dashboard/templates"
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            取消
          </Link>
          <button
            type="submit"
            className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
          >
            创建模板
          </button>
        </div>
      </form>
    </div>
  );
}



