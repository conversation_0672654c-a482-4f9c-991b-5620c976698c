/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-06 17:43:10
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-05-06 17:52:13
 * @FilePath: \wp-sitemgr\app\layout.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import '@/app/ui/global.css';
import { inter } from '@/app/ui/fonts';

export const metadata = {
  title: 'WordPress Site Manager',
  description: 'Manage WordPress sites across multiple cloud platforms with CDN integration',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`} suppressHydrationWarning={true}>
        {children}
      </body>
    </html>
  );
}

