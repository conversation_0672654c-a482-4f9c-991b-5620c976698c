/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-07 17:38:09
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-09-25 17:21:01
 * @FilePath: \wp-sitemgr\app\lib\config.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
/**
 * Application configuration settings
 */

// Database configuration
export const dbConfig = {
  url: process.env.POSTGRES_URL || "postgresql://postgres:postgres@127.0.0.1:54321/wpmgr",
  sslRequired: false,
  poolSize: 10,
  connectionTimeout: 30000, // 30 seconds
};

// Server configuration
export const serverConfig = {
  port: process.env.PORT || 3000,
  host: process.env.HOST || 'localhost',
  environment: process.env.NODE_ENV || 'development',
};

// WordPress defaults - simplified to only version
export const wordpressDefaults = {
  version: '6.8.2',
  adminUser: 'admin',
  dbPrefix: 'wp_',
};

// Business group configurations with fixed deployment features
export const businessGroupDefaults = {
  edm: {
    name: 'EDM业务组',
    cloudPlatform: 'aliyun',
    deploymentType: 'standard',
    environmentType: 'ecs',
    dnsProvider: 'aliyun_dns',
    apiEndpoint: '/api/deploy/edm',
  },
  shop: {
    name: 'Shop业务组', 
    cloudPlatform: 'aws',
    deploymentType: 'standard',
    environmentType: 'ec2',
    dnsProvider: 'cloudflare',
    apiEndpoint: '/api/deploy/shop',
  },
  marketing: {
    name: 'Marketing业务组',
    cloudPlatform: 'aliyun',
    deploymentType: 'kubernetes',
    environmentType: 'ack',
    dnsProvider: 'cloudflare',
    apiEndpoint: '/api/deploy/marketing',
  }
};

// Cloud provider settings
export const cloudProviderDefaults = {
  aliyun: {
    regions: ['ap-southeast-1', 'ap-northeast-1', 'cn-hangzhou', 'cn-beijing'],
    instanceTypes: ['ecs.g6.large', 'ecs.g6.xlarge', 'ecs.g6.2xlarge'],
  },
  aws: {
    regions: ['us-east-1', 'us-west-2', 'ap-southeast-1', 'eu-west-1'],
    instanceTypes: ['t3.medium', 't3.large', 'm5.large', 'm5.xlarge'],
  },
};

// Pagination settings
export const paginationConfig = {
  itemsPerPage: 6,
};

// DNS resolution API endpoints
export const dnsConfig = {
  checkResolution: '/api/dns/check',
  checkSsl: '/api/dns/ssl-check',
  records: '/api/dns/records',
  updateRecord: '/api/dns/update',
  deleteRecord: '/api/dns/delete',
  dnsBaseServerApi: 'http://35.172.21.28:8000',
};


