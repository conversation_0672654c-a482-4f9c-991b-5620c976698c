/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-07 11:20:23
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-29 14:41:18
 * @FilePath: \wp-sitemgr\app\dashboard\providers\page.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { Suspense } from 'react';
import Link from 'next/link';
import { getConnection } from '@/app/lib/db';

// Use the connection pool
const sql = getConnection();

async function getProviders() {
  try {
    const providers = await sql`
      SELECT 
        id, 
        name, 
        type, 
        account_id as "accountId", 
        region, 
        capabilities::json as capabilities, 
        active
      FROM cloud_providers
      ORDER BY name ASC
    `;

    // Ensure capabilities is properly parsed for each provider
    return Array.isArray(providers) ? providers.map(provider => ({
      ...provider,
      capabilities: typeof provider.capabilities === 'string'
        ? JSON.parse(provider.capabilities)
        : (provider.capabilities || {})
    })) as Provider[] : [];
  } catch (error) {
    console.error('Failed to fetch providers:', error);
    return [];
  }
}
type Provider = {
  id: string;
  name: string;
  type: string;
  accountId: string;
  region: string;
  capabilities: any;
  active: boolean;
};

function ProviderCard({ provider }: { provider: Provider }) {
  // Ensure capabilities is an object, default to empty object if null/undefined
  const capabilities = provider.capabilities || {};

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">{provider.name}</h2>
        <span className={`rounded-full px-3 py-1 text-sm ${provider.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
          {provider.active ? '有效' : '无效'}
        </span>
      </div>
      <div className="mt-2 text-sm text-gray-500">账号 ID: {provider.accountId}</div>
      <div className="mt-4 space-y-2">
        <div className="flex justify-between">
          <span className="text-gray-500">地区:</span>
          <span>{provider.region}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">RDS 实例:</span>
          <span>{capabilities.rds ? '✓' : '✗'}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">负载均衡:</span>
          <span>{capabilities.loadbalancer ? '✓' : '✗'}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">K8s 集群:</span>
          <span>{capabilities.k8s ? '✓' : '✗'}</span>
        </div>
      </div>
      <div className="mt-4 flex space-x-2">
        {capabilities.rds && (
          <button className="rounded bg-blue-50 px-3 py-1 text-sm text-blue-700 hover:bg-blue-100">管理RDS</button>
        )}
        {capabilities.loadbalancer && (
          <button className="rounded bg-blue-50 px-3 py-1 text-sm text-blue-700 hover:bg-blue-100">负载均衡</button>
        )}
        {capabilities.k8s && (
          <button className="rounded bg-blue-50 px-3 py-1 text-sm text-blue-700 hover:bg-blue-100">K8s集群</button>
        )}
      </div>
    </div>
  );
}

async function ProvidersList() {
  const providers = await getProviders();

  if (!providers || providers.length === 0) {
    return (
      <div className="mt-6 p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
        <p className="text-gray-500 text-center">没有找到云平台。添加第一个云平台开始。</p>
      </div>
    );
  }

  return (
    <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {providers.map((provider) => (
        <ProviderCard key={provider.id || provider.name} provider={provider} />
      ))}
    </div>
  );
}

export default function CloudProvidersPage() {
  return (
    <div className="w-full">
      <div className="flex w-full items-center justify-between">
        <h1 className="text-2xl font-bold">云平台</h1>
        <Link href="/dashboard/providers/create" className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600">
          添加云平台
        </Link>
      </div>

      <Suspense fallback={<div>加载云平台...</div>}>
        <ProvidersList />
      </Suspense>
    </div>
  );
}


