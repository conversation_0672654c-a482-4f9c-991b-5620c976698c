/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-05-06 17:43:10
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-30 11:10:28
 * @FilePath: \wp-sitemgr\app\ui\dashboard\nav-links.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
'use client';

import {
  UserGroupIcon,
  HomeIcon,
  ServerIcon,
  GlobeAltIcon,
  CloudIcon,
  DocumentDuplicateIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import clsx from 'clsx';

// Map of links to display in the side navigation.
const links = [
  { name: '仪表板', href: '/dashboard', icon: HomeIcon },
  { name: 'WordPress站点', href: '/dashboard/sites', icon: GlobeAltIcon },
  // { name: '云平台', href: '/dashboard/providers', icon: CloudIcon },
  { name: '部署模板', href: '/dashboard/templates', icon: DocumentDuplicateIcon },
  { name: '业务组管理', href: '/dashboard/business-groups', icon: BuildingOfficeIcon },
  // { name: '服务器', href: '/dashboard/servers', icon: ServerIcon },
  { name: 'DNS工具箱', href: '/dashboard/dns', icon: UserGroupIcon },
];

export default function NavLinks() {
  const pathname = usePathname();

  return (
    <>
      {links.map((link) => {
        const LinkIcon = link.icon;
        return (
          <Link
            key={link.name}
            href={link.href}
            className={clsx(
              'flex h-[48px] grow items-center justify-center gap-2 rounded-md bg-gray-50 p-3 text-sm font-medium hover:bg-sky-100 hover:text-blue-600 md:flex-none md:justify-start md:p-2 md:px-3',
              {
                'bg-sky-100 text-blue-600': pathname === link.href,
              },
            )}
          >
            <LinkIcon className="w-6" />
            <p className="hidden md:block">{link.name}</p>
          </Link>
        );
      })}
    </>
  );
}




