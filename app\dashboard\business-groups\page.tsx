'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

interface BusinessGroup {
  id: string;
  name: string;
  code: string;
  description?: string;
  cloudPlatform: string;
  deploymentType: string;
  environmentType: string;
  dnsProvider: string;
  apiEndpoint: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    wordpressSites: number;
  };
}

export default function BusinessGroupsPage() {
  const [businessGroups, setBusinessGroups] = useState<BusinessGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBusinessGroups();
  }, []);

  const fetchBusinessGroups = async () => {
    try {
      const response = await fetch('/api/business-groups');
      if (!response.ok) throw new Error('Failed to fetch business groups');
      const data = await response.json();
      setBusinessGroups(data);
    } catch (err) {
      setError('Failed to load business groups');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个业务组吗？')) return;
    
    try {
      const response = await fetch(`/api/business-groups/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete business group');
      fetchBusinessGroups();
    } catch (err) {
      setError('Failed to delete business group');
    }
  };

  if (loading) return <div className="text-center py-10">加载中...</div>;
  if (error) return <div className="text-center py-10 text-red-600">{error}</div>;

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">业务组管理</h1>
          <p className="text-gray-600">管理业务组配置和部署设置</p>
        </div>
        <Link
          href="/dashboard/business-groups/new"
          className="flex items-center gap-1 rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600"
        >
          <PlusIcon className="h-5 w-5" />
          创建业务组
        </Link>
      </div>

      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代码</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">云平台</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部署类型</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">站点数量</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {businessGroups.map((group) => (
              <tr key={group.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{group.name}</div>
                    <div className="text-sm text-gray-500">{group.description}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group.cloudPlatform}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group.deploymentType}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group._count.wordpressSites}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    group.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {group.active ? '活跃' : '禁用'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <Link
                    href={`/dashboard/business-groups/edit/${group.id}`}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    <PencilIcon className="h-4 w-4 inline" />
                  </Link>
                  <button
                    onClick={() => handleDelete(group.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <TrashIcon className="h-4 w-4 inline" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}