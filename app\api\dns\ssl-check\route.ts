/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-08-30 13:34:02
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-09-25 17:50:44
 * @FilePath: \wp-sitemgr\app\api\dns\ssl-check\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { dnsConfig } from '@/app/lib/config';

// POST /api/dns/ssl-check - Check SSL type flexible/full/strict
export async function POST(request: Request) {
  try {
    const { domain } = await request.json();

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      );
    }


    try {
      const sslResponse = await fetch(`${dnsConfig.dnsBaseServerApi}/api/checkssl`, {
        method: 'POST',
        headers: {
          'auth-header': `${process.env.DNS_MANAGER_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ domain:domain })
      });

      if (!sslResponse.ok) {
        console.error('Agent API request failed:', sslResponse.text());
        throw new Error('Agent API request failed');
      }

      const sslData = await sslResponse.json();

      return NextResponse.json({
        domain,
        status: sslData.success, 
        sslType: sslData.ssl_type, // flexible/full/strict
      });

    } catch (error) {
      console.error('Agent API error:', error);
      // return simulateSslCheck(domain);
      return NextResponse.json(
        { error: 'Failed to check SSL status' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('SSL check error:', error);
    return NextResponse.json(
      { error: 'Failed to check SSL status' },
      { status: 500 }
    );
  }
}